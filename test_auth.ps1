# Test Authentication Endpoints

Write-Host "Testing Authentication Endpoints..." -ForegroundColor Green

# Test Signup
Write-Host "`nTesting Signup..." -ForegroundColor Yellow
$signupBody = @{
    username = "testuser"
    email = "<EMAIL>"
    password = "password123"
    firstName = "Test"
    lastName = "User"
} | ConvertTo-Json

try {
    $signupResponse = Invoke-RestMethod -Uri "http://localhost:8081/api/auth/signup" -Method Post -ContentType "application/json" -Body $signupBody
    Write-Host "Signup successful!" -ForegroundColor Green
    Write-Host "Access Token: $($signupResponse.accessToken.Substring(0, 20))..." -ForegroundColor Cyan
    Write-Host "Refresh Token: $($signupResponse.refreshToken.Substring(0, 20))..." -ForegroundColor Cyan
} catch {
    Write-Host "Signup failed: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response: $responseBody" -ForegroundColor Red
    }
}

# Test Login
Write-Host "`nTesting Login..." -ForegroundColor Yellow
$loginBody = @{
    username = "testuser"
    password = "password123"
} | ConvertTo-Json

try {
    $loginResponse = Invoke-RestMethod -Uri "http://localhost:8081/api/auth/login" -Method Post -ContentType "application/json" -Body $loginBody
    Write-Host "Login successful!" -ForegroundColor Green
    Write-Host "Access Token: $($loginResponse.accessToken.Substring(0, 20))..." -ForegroundColor Cyan
    Write-Host "Refresh Token: $($loginResponse.refreshToken.Substring(0, 20))..." -ForegroundColor Cyan
} catch {
    Write-Host "Login failed: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response: $responseBody" -ForegroundColor Red
    }
}

Write-Host "`nTest completed!" -ForegroundColor Green
