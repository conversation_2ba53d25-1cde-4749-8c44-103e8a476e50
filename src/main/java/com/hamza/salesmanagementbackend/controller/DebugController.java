package com.hamza.salesmanagementbackend.controller;

import com.hamza.salesmanagementbackend.entity.User;
import com.hamza.salesmanagementbackend.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("/api/debug")
@RequiredArgsConstructor
public class DebugController {

    private final UserRepository userRepository;

    @GetMapping("/user/{username}")
    public ResponseEntity<Map<String, Object>> getUserInfo(@PathVariable String username) {
        Optional<User> userOpt = userRepository.findByUsername(username);
        
        Map<String, Object> response = new HashMap<>();
        
        if (userOpt.isPresent()) {
            User user = userOpt.get();
            response.put("exists", true);
            response.put("username", user.getUsername());
            response.put("email", user.getEmail());
            response.put("enabled", user.isEnabled());
            response.put("accountNonExpired", user.isAccountNonExpired());
            response.put("accountNonLocked", user.isAccountNonLocked());
            response.put("credentialsNonExpired", user.isCredentialsNonExpired());
            response.put("role", user.getRole());
            response.put("createdAt", user.getCreatedAt());
        } else {
            response.put("exists", false);
            response.put("message", "User not found");
        }
        
        return ResponseEntity.ok(response);
    }

    @GetMapping("/users/all")
    public ResponseEntity<Map<String, Object>> getAllUsers() {
        var users = userRepository.findAll();
        Map<String, Object> response = new HashMap<>();
        response.put("count", users.size());
        response.put("users", users.stream().map(user -> {
            Map<String, Object> userInfo = new HashMap<>();
            userInfo.put("username", user.getUsername());
            userInfo.put("email", user.getEmail());
            userInfo.put("enabled", user.isEnabled());
            userInfo.put("accountNonLocked", user.isAccountNonLocked());
            userInfo.put("role", user.getRole());
            return userInfo;
        }).toList());
        
        return ResponseEntity.ok(response);
    }
}
