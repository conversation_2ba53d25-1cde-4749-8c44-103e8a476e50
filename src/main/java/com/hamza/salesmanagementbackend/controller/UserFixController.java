package com.hamza.salesmanagementbackend.controller;

import com.hamza.salesmanagementbackend.entity.User;
import com.hamza.salesmanagementbackend.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("/api/debug")
@RequiredArgsConstructor
public class UserFixController {

    private final UserRepository userRepository;

    @PostMapping("/fix-user/{username}")
    public ResponseEntity<Map<String, Object>> fixUserAccount(@PathVariable String username) {
        Optional<User> userOpt = userRepository.findByUsername(username);
        
        Map<String, Object> response = new HashMap<>();
        
        if (userOpt.isPresent()) {
            User user = userOpt.get();
            
            // Store old values for comparison
            Map<String, Object> oldValues = new HashMap<>();
            oldValues.put("enabled", user.isEnabled());
            oldValues.put("accountNonExpired", user.isAccountNonExpired());
            oldValues.put("accountNonLocked", user.isAccountNonLocked());
            oldValues.put("credentialsNonExpired", user.isCredentialsNonExpired());
            
            // Fix the account status
            user.setEnabled(true);
            user.setAccountNonExpired(true);
            user.setAccountNonLocked(true);
            user.setCredentialsNonExpired(true);
            
            // Save the updated user
            User savedUser = userRepository.save(user);
            
            // Store new values
            Map<String, Object> newValues = new HashMap<>();
            newValues.put("enabled", savedUser.isEnabled());
            newValues.put("accountNonExpired", savedUser.isAccountNonExpired());
            newValues.put("accountNonLocked", savedUser.isAccountNonLocked());
            newValues.put("credentialsNonExpired", savedUser.isCredentialsNonExpired());
            
            response.put("success", true);
            response.put("username", username);
            response.put("message", "User account status fixed");
            response.put("oldValues", oldValues);
            response.put("newValues", newValues);
        } else {
            response.put("success", false);
            response.put("message", "User not found");
        }
        
        return ResponseEntity.ok(response);
    }

    @PostMapping("/fix-all-users")
    public ResponseEntity<Map<String, Object>> fixAllUsers() {
        var users = userRepository.findAll();
        int fixedCount = 0;
        
        for (User user : users) {
            boolean needsFix = !user.isEnabled() || !user.isAccountNonExpired() || 
                              !user.isAccountNonLocked() || !user.isCredentialsNonExpired();
            
            if (needsFix) {
                user.setEnabled(true);
                user.setAccountNonExpired(true);
                user.setAccountNonLocked(true);
                user.setCredentialsNonExpired(true);
                userRepository.save(user);
                fixedCount++;
            }
        }
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("totalUsers", users.size());
        response.put("fixedUsers", fixedCount);
        response.put("message", "Fixed " + fixedCount + " users out of " + users.size() + " total users");
        
        return ResponseEntity.ok(response);
    }
}
