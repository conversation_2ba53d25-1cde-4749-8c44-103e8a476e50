spring.application.name=SalesManagementBackend

# Database Configuration (MySQL)
spring.datasource.url=***************************************************************************************************************************************
spring.datasource.driverClassName=com.mysql.cj.jdbc.Driver
spring.datasource.username=root
spring.datasource.password=
spring.jpa.database-platform=org.hibernate.dialect.MySQLDialect
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true

# Server Configuration - Use different port to avoid conflict
server.port=8081

# Logging Configuration
logging.level.com.hamza.salesmanagementbackend=DEBUG

# JWT Configuration
jwt.secret=mySecretKey12345678901234567890123456789012345678901234567890
jwt.expiration=86400000

# CORS Configuration
cors.allowed-origins=*
cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
cors.allowed-headers=*
cors.max-age=3600
