# Test different password variations for hamza user

Write-Host "Testing different password variations for hamza user..." -ForegroundColor Green

$passwords = @("password", "123456", "hamza", "admin", "password123", "123", "hamza123")

foreach ($password in $passwords) {
    Write-Host "`nTrying password: $password" -ForegroundColor Yellow
    
    $loginBody = @{
        username = "hamza"
        password = $password
    } | ConvertTo-Json

    try {
        $loginResponse = Invoke-RestMethod -Uri "http://localhost:8081/api/auth/login" -Method Post -ContentType "application/json" -Body $loginBody
        Write-Host "SUCCESS! Login worked with password: $password" -ForegroundColor Green
        Write-Host "Access Token: $($loginResponse.accessToken.Substring(0, 20))..." -ForegroundColor Cyan
        break
    } catch {
        Write-Host "Failed with password: $password" -ForegroundColor Red
    }
}

Write-Host "`nIf none worked, let's create a new user with known credentials..." -ForegroundColor Yellow

# Create a new user with known password
$signupBody = @{
    username = "hamza2"
    email = "<EMAIL>"
    password = "password123"
    firstName = "Hamza"
    lastName = "Test"
} | ConvertTo-Json

try {
    $signupResponse = Invoke-RestMethod -Uri "http://localhost:8081/api/auth/signup" -Method Post -ContentType "application/json" -Body $signupBody
    Write-Host "New user created successfully!" -ForegroundColor Green
    
    # Test login with new user
    $loginBody = @{
        username = "hamza2"
        password = "password123"
    } | ConvertTo-Json
    
    $loginResponse = Invoke-RestMethod -Uri "http://localhost:8081/api/auth/login" -Method Post -ContentType "application/json" -Body $loginBody
    Write-Host "Login successful with new user!" -ForegroundColor Green
    Write-Host "Access Token: $($loginResponse.accessToken.Substring(0, 20))..." -ForegroundColor Cyan
} catch {
    Write-Host "Failed to create/login new user: $($_.Exception.Message)" -ForegroundColor Red
}
